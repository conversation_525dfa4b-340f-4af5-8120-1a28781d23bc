import * as THREE from 'three';
import {initMotor} from '../../motor.js';
import {initWall} from '../../wall.js';
import {initZouxiangjia} from '../../zouxiangjia.js';
import {initBanka} from '../../banka.js';

export async function initModel(RmCabinetModelList,RmRoomList){
    const model = new THREE.Group();
    model.add(initWall(RmRoomList));
    model.add(initMotor(RmCabinetModelList));
    model.add(initZouxiangjia(RmRoomList)); // 添加走向架模型
    // model.add(initBanka());
    return model;
}