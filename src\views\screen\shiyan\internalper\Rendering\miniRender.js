import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { initjhj, initjhj2 } from '../../jiaohuanji';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass.js';
import { GammaCorrectionShader } from 'three/examples/jsm/shaders/GammaCorrectionShader.js';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { SMAAPass } from 'three/examples/jsm/postprocessing/SMAAPass.js';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { EventBus } from '@/utils/eventBus';

        const scene1 = new THREE.Scene();
        // const camera1 = new THREE.PerspectiveCamera(75, 350/400, 0.1, 1000);
        // 替换原有的 PerspectiveCamera 参数
        const aspect = 280 / 400; // 修改为新的宽高比
        const viewSize = 3.6; // 增加视野范围以补偿宽高比的变化

        const camera1 = new THREE.OrthographicCamera(
        -viewSize * aspect / 2,  // left
        viewSize * aspect / 2,   // right
        viewSize / 2,            // top
        -viewSize / 2,           // bottom
        -8,                     // near
        1000                     // far
        );
            const renderer = new THREE.WebGLRenderer({
            // alpha:true,
            antialias: true,
            // powerPreference: 'high-performance', stencil: true,
            });
            const light = new THREE.AmbientLight(0xffffff, 5);
            scene1.add(light);
export async function miniAddJhj(buttonCount,currentMouseNumber,Motor2parameters){
    console.log('buttonCount',buttonCount);
    console.log('currentMouseNumber',currentMouseNumber);
    console.log('Motor2parameters',Motor2parameters);


    // 使用新的initjhj函数，传递参数对象
    let jhj = await initjhj(
        currentMouseNumber,
        3,
        buttonCount,
    );

    scene1.add(jhj);
    renderer.render(scene1, camera1);
}

export async function getAllJhj(startUForMotor2, occupiedUForMotor2, Motor2parameters, buttonCount, equipmentList){
    // 移除现有的交换机
    scene1.children.forEach(child => {
        scene1.remove(scene1.getObjectByName('jhj2'));
    });

    // 确保buttonCount有效
    buttonCount = buttonCount || 48;

    const height = Motor2parameters.height;

    // 创建所有交换机
    for (let i = 0; i < startUForMotor2.length; i++) {
        // 获取当前设备的完整数据
        const equipmentData = equipmentList && equipmentList[i] ? equipmentList[i] : null;

        // 使用initjhj函数，传递正确的参数
        let jhj = await initjhj(
            startUForMotor2[i],
            occupiedUForMotor2[i],
            buttonCount, // 传递实际的buttonCount
            equipmentData // 传递完整的设备数据
        );

        jhj.name = 'jhj2';
        scene1.add(jhj);
    }

    renderer.render(scene1, camera1);
}


export async function initMiniRender(motor2, miniContainer) {
    const existingCanvas = miniContainer.querySelector('canvas');
    if (existingCanvas) {
        miniContainer.removeChild(existingCanvas);
    }
    motor2.name = 'motor2';
    const oldMotor2 = scene1.getObjectByName('motor2');
    if (oldMotor2) {
        scene1.remove(oldMotor2);
    }

    scene1.add(motor2);
    camera1.position.set(-0.83, 0.1, 1.9);
    camera1.lookAt(0, 0, 0);

    renderer.setSize(window.innerWidth * 0.75, window.innerHeight);
    renderer.domElement.style.width = '100%';
    renderer.domElement.style.height = '100%';
    renderer.domElement.style.border = 'none';
    renderer.domElement.style.outline = 'none';
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setClearAlpha(0);
    const pixelRatio = Math.min(window.devicePixelRatio, 2);
    renderer.setPixelRatio(pixelRatio);
    renderer.antialias = true; // 启用渲染器的抗锯齿
    renderer.outputEncoding = THREE.sRGBEncoding;

    // 后处理设置
    const composer = new EffectComposer(renderer);
    const renderPass = new RenderPass(scene1, camera1);
    composer.addPass(renderPass);

    // 添加描边效果
    const v2 = new THREE.Vector2(window.innerWidth * 0.75, window.innerHeight);
    const outlinePass = new OutlinePass(v2, scene1, camera1);
    outlinePass.visibleEdgeColor.set(0x3e67f9);
    outlinePass.edgeThickness = 4.0;
    outlinePass.edgeStrength = 10;
    outlinePass.pulsePeriod = 2;
    composer.addPass(outlinePass);

    // 添加伽马校正
    const gammaPass = new ShaderPass(GammaCorrectionShader);
    composer.addPass(gammaPass);

    // 添加SMAA抗锯齿
    const smaaPass = new SMAAPass(window.innerWidth , window.innerHeight,{
    edgeDetectionThreshold: 0.025,
    maxSearchSteps: 64,
    cornerDetection: true,
    temporalEnabled: true
    });
    composer.addPass(smaaPass);

    // 点击事件处理
    miniContainer.addEventListener('click', function(event) {
        const rect = miniContainer.getBoundingClientRect();
        const x = ((event.clientX - rect.left) / miniContainer.clientWidth) * 2 - 1;
        const y = -((event.clientY - rect.top) / miniContainer.clientHeight) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(new THREE.Vector2(x, y), camera1);

        const switches = scene1.children.filter(obj => obj.name === 'jhj2');
        const intersects = raycaster.intersectObjects(switches, true);

        if (intersects.length > 0) {
            const selectedSwitch = intersects[0].object;

            // 检查当前选中的对象是否已经在发光列表中
            const index = outlinePass.selectedObjects.findIndex(
                obj => obj.uuid === selectedSwitch.uuid
            );

            if (index === -1) {
                // 如果对象不在发光列表中，添加它
                outlinePass.selectedObjects = [selectedSwitch];

                // 获取交换机的父对象（Group）
                let switchGroup = selectedSwitch.parent;

                // 准备交换机数据
                const switchData = {
                    userData: switchGroup.userData || {}
                };

                // 通过EventBus发送交换机数据
                // console.log('发送交换机数据:', switchData);
                EventBus.$emit('switch-clicked', switchData);
            } else {
                // 如果对象已经在发光列表中，移除它
                outlinePass.selectedObjects = [];
                // 发送清除事件
                EventBus.$emit('switch-cleared');
            }

            composer.render();
        } else {
            outlinePass.selectedObjects = [];
            composer.render();
        }
    });

    renderer.outputEncoding = THREE.sRGBEncoding;
    miniContainer.appendChild(renderer.domElement);

    const controls = new OrbitControls(camera1, renderer.domElement);
    controls.addEventListener('change', function() {
        composer.render();
    });

    // 初始渲染
    composer.render();

    // 添加窗口大小调整处理
    window.addEventListener('resize', function() {
        const width = window.innerWidth * 0.75;
        const height = window.innerHeight;

        camera1.updateProjectionMatrix();
        renderer.setSize(width, height);
        composer.setSize(width, height);
    });

    return scene1;
}

export function updateShow3DPattern2(newVal, currentMouseNumber, buttonCount, height, startU, occupiedU) {
    currentMouseNumber--;

    // 移除旧的悬停效果
    let oldjhj3 = scene1.getObjectByName('jhj3');
    if (oldjhj3) {
       scene1.remove(oldjhj3);
    }

    // 移除旧的连续区域悬停效果
    let oldHoverGroup = scene1.getObjectByName('hover-group');
    if (oldHoverGroup) {
        scene1.remove(oldHoverGroup);
    }

    // 如果newVal为false，只需要移除模型并渲染场景
    if (!newVal) {
        renderer.render(scene1, camera1);
        return;
    }

    // 确保buttonCount有效，默认为48
    buttonCount = buttonCount || 48;

    // 如果newVal为true，创建新的模型
    if (startU && occupiedU) {
        // 连续区域的悬停效果，使用initjhj2
        initjhj2(true, startU, occupiedU, currentMouseNumber, buttonCount, height).then(jhj => {
            jhj.name = 'hover-group';
            scene1.add(jhj);
            renderer.render(scene1, camera1);
        });
    } else {
        // 普通的单个悬停效果，使用initjhj2，传递正确的buttonCount和height
        initjhj2(newVal, null, null, currentMouseNumber, buttonCount, height).then((group) => {
        group.name = 'jhj3';
        scene1.add(group);

        // 渲染场景
        renderer.render(scene1, camera1);
    })
}
}
