<template>
    <div class="maindiv">
        <div class="content-container">
            <!-- <dv-full-screen-container> -->
            <!-- <dv-border-box-11 title="智慧综合化维护">
        </dv-border-box-11> -->
            <div class="left" id="left">
                <dv-border-box-7 :color="['#214095', '#D3E1F8']">
                    <!-- <div class="title">数据</div> -->
                    <div class="left-one">
                        <el-select v-model="form.roomName" placeholder="请选所属机房" filterable clearable
                            @change="selectRoom" class="custom-select">
                            <el-option v-for="(item, index) in RmRoomList" :key="index" :label="item.roomName"
                                :value="item.uuid">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="left-two">
                        <div class="title">
                            <span class="title-line"></span>
                            <span class="title-text">PUE</span>
                        </div>
                    </div>
                    <div class="left-three">
                        <div class="title">
                            <span class="title-line"></span>
                            <span class="title-text">告警</span>
                        </div>
                        <div class="left-cont">
                            <el-row>
                                <el-col :span="8">
                                    <dv-border-box-12>
                                        <div class="left-cont-box">
                                            <div class="left-cont-box-num" style="color:#F43A4A;">0</div>
                                            <div class="left-cont-box-title">紧急告警</div>
                                        </div>
                                    </dv-border-box-12>
                                </el-col>
                                <el-col :span="8">
                                    <dv-border-box-12>
                                        <div class="left-cont-box">
                                            <div class="left-cont-box-num" style="color:#F08F48;">2</div>
                                            <div class="left-cont-box-title">紧急告警</div>
                                        </div>
                                    </dv-border-box-12>
                                </el-col>
                                <el-col :span="8">
                                    <dv-border-box-12>
                                        <div class="left-cont-box">
                                            <div class="left-cont-box-num" style="color:#00FCB9;">4</div>
                                            <div class="left-cont-box-title">紧急告警</div>
                                        </div>
                                    </dv-border-box-12>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                    <div class="left-four">
                        <div class="title">
                            <span class="title-line"></span>
                            <span class="title-text">容量管理</span>
                        </div>
                    </div>
                </dv-border-box-7>

            </div>
            <div class="main">
                <div class="main-top">
                    <el-row justify="center">
                        <el-col :span="12">
                            <el-input placeholder="搜索" v-model="input" clearable class="custom-input"
                                style="width:98%;">
                            </el-input>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-search large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-arrow-left large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-full-screen large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-pie-chart large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-share large-red-icon"></i>
                            </div>
                        </el-col>
                        <el-col :span="2">
                            <div class="text-center">
                                <i class="el-icon-s-fold large-red-icon"></i>
                            </div>
                        </el-col>
                    </el-row>
                </div>
                <div class="main-room">
                    <room3D @update-selected-cabinet="handleSelectedCabinetUpdate"
                            @update-RmRoomList="updateRmRoomList"
                            @update-RmCabinetModelList="updateRmCabinetModelList"
                            :show3DPattern = show3DPattern
                    />
                </div>
            </div>
            <div class="right" id="right">
                <Editor :selectedCabinet = "this.selectedCabinet"
                @updateShow3DPattern="updateShow3DPattern"
                @switch-clicked="handleSwitchClicked"
                @switch-cleared="handleSwitchCleared"/>
            </div>

            <!-- 交换机信息面板 -->
            <MianBan />

            <!-- 槽位信息面板 -->
            <CaoWei />



            <!-- 当前日期+当前星期 -->
            <!-- <div class="datae">
            {{ nowDate + ' ' + nowWeek }}
        </div> -->
            <!-- 当前时间 -->
            <!-- <div class="timer">
            {{ nowTime }}
        </div> -->
        </div>
    </div>
</template>

<script>
// import room3D from "@/views/screen/index3d.vue";
import room3D from "@/views/screen/shiyan/internalper/ThreeScene.vue";
import { listRmRooms } from "@/api/room/RmRoom";
import Editor from "./shiyan/internalper/Editor.vue";
import MianBan from "./shiyan/internalper/MianBan.vue";
import CaoWei from "./shiyan/internalper/CaoWei.vue";
export default {
    name: 'screenindex',
    components: { room3D, Editor, MianBan, CaoWei },
    data() {
        return {
            input: '',
            loadinge: false,
            // dialogTableVisible: false

            // 机房管理表格数据
            RmRoomList: [],
            // 表单参数
            form: {},
            selectedCabinet : null,
            index : null,
            RmCabinetModelList: [],
            RmRoomList: [],
            show3DPattern:false,

        }
    },
    mounted() {
        // this.currentTime()
        // this.getRoomList();
    },
    // 销毁定时器
    beforeDestroy() {
        // console.log("销毁定时器");
        // clearInterval(this.getDate) // 在Vue实例销毁前，清除时间定时器
        // clearInterval(this.monitortime)
    },
    created() {
        this.getRoomList();
    },
    methods: {
        /** 查询机房管理列表 */
        selectRoom() {
            this.getRoomList();
        },
        getRoomList() {
            this.loading = true;
            listRmRooms().then(response => {
                this.RmRoomList = response.rows;
                if (this.RmRoomList.length > 0 && this.form.roomName==null) {
                    this.form.roomName = this.RmRoomList[0].uuid
                }
            });
        },
        handleSelectedCabinetUpdate(newCabinet) {
            this.selectedCabinet = newCabinet; // 更新 selectedCabinet 数据
            console.log('Selected Cabinet:', this.selectedCabinet);

        },
        updateRmCabinetModelList(RmCabinetModelList){
            this.RmCabinetModelList = RmCabinetModelList;
        },
        updateRmRoomList(RmRoomList){
            this.getRoomList = RmRoomList;
        },
        updateShow3DPattern(show3DPattern){
            this.show3DPattern = show3DPattern;
        },
        // 处理交换机点击事件
        handleSwitchClicked() {
            // 获取main元素
            const mainElement = document.querySelector('.main');
            if (mainElement) {
                // 将main元素向左平移20%
                mainElement.style.transform = 'translateX(-20%)';
            }
        },
        // 处理交换机清除事件
        handleSwitchCleared() {
            // 获取main元素
            const mainElement = document.querySelector('.main');
            if (mainElement) {
                // 恢复main元素位置
                mainElement.style.transform = 'translateX(0)';
            }
        },
        // updateSelectedCabinet(cabinetData,index) {
        // if(cabinetData && index){
        //   this.selectedCabinet = cabinetData;
        //   this.index = index;
        //   // console.log('this.selectedCabinet====',this.selectedCabinet);
        //   // console.log('this.index====',this.index);
        // }

    // },
}

}
</script>

<style lang="scss" scoped>
.maindiv {
    // width: 1920px;
    // height: 1080px;
    width: 100%;
    height: 100vh;
    // background-image: url('../../assets/images/datavbg.png');
    background: url('../../assets/images/screen/1666271024838094849.jpg');
    overflow: hidden;
    background-size: cover;
}

::v-deep .custom-input .el-input__inner {
    background-color: transparent;
    border-color: #389BDA;
}

//select样式
::v-deep .custom-select .el-input__inner {
    border: none;
    background-color: transparent;
    color: #389BDA;
    font-size: 18px;
    font-weight: bold;
}

::v-deep .custom-select .el-input.is-focus .el-input__inner {
    border: none;
    box-shadow: none;
}

::v-deep .custom-select .el-input__suffix .el-input__icon {
    color: #389BDA;
}

::v-deep .custom-select .el-select-dropdown__item {
    color: #389BDA;
    font-size: 20px;
    font-weight: bold;
}

::v-deep .custom-select .el-select-dropdown__item.selected,
::v-deep .custom-select .el-select-dropdown__item:hover {
    background-color: #389BDA;
    color: white;
}

/* 设置下拉列表的背景颜色 */
::v-deep .custom-select .el-select-dropdown {
    background-color: transparent;
    // background-color: rgba(255, 255, 255, 0.8); /* 这里可以根据需要调整透明度和颜色 */
}
::v-deep .el-col {
    padding-left: 5px;
    padding-right: 5px;
    box-sizing: border-box; /* 确保内边距不会影响元素的总宽度 */
}
.content-container {
    display: flex;
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
}

// .cons{
//   position: absolute;
//   //  border:1px solid red;
//    width: 100%;
//    height:calc(100% - 40px);
// }
.title {
    padding-top: 10px;
    padding-left: 10px;
    color: #fff;
    // color:#7EC699;
    // border:1px solid red;
    position: relative;
    align-items: center;
    // justify-content: center;
    font-weight: bold;
    // height:25px;
    // font-size:16px;
    //  border:1px solid red;
}

.titles {
    color: #fff;
    position: relative;
    align-items: center;
    text-align: center;
    height: 30px;
    width: 100%;
}

#left.hidden {
    opacity: 0;
    transform: translateX(-100%);
    transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    position: absolute; /* 使用绝对定位，不影响其他元素 */
    pointer-events: none; /* 隐藏时不接收鼠标事件 */
}

.left {
    width: 25%;
    // height: calc(100% - 60px);
    height: 100%;
    // position: absolute;
    top: 0px;
    left: 0px;
    position: relative; /* 必须设置定位 */
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    opacity: 1;
    transform: translateX(0);
    overflow: hidden; /* 防止内容溢出 */



    // border: 1px solid red;

    .left-one {
        height: 80px;
        // bottom:20px;
        // color: #389BDA;
        // border-radius: 3px;
        padding: 20px 20px 10px 20px;
        // align-items: center;
        // padding: 10px;

    }

    .left-two {
        height: 300px;
        // color: #389BDA;
        padding: 10px 20px;

    }

    .left-three {
        height: 200px;
        // color: #389BDA;
        padding: 10px 20px;

        .left-cont-box {
            width:100%;
            height: 100px;
            padding: 10px;
            // display: flex;
            // margin-top: 10px;
        }

        .left-cont-box-num {
            height: 70%;
            font-size: 28px;
            color: #fff;
            font-weight: bold;
            // border:1px solid red;
            display: flex;
            align-items: center; /* 垂直居中 */
            justify-content: center; /* 水平居中（可选，若需要水平也居中则添加） */
        }

        .left-cont-box-title {
            font-size: 16px;
            color: #fff;
            font-weight: bold;
        }
    }

    .left-four {
        // height:250px;
        height: calc(100% - 630px);
        // color: #389BDA;
        padding: 10px 20px;

    }

    .left-cont {
        margin-top: 30px;
        text-align: center;
    }

    .title {
        height: 20px;
        display: flex;

        .title-line {
            height: 15px;
            width: 3px;
            background-color: #00FDBA;
        }

        .title-text {
            padding-left: 8px;
            color: #389BDA;
        }
    }
}

.text-center {
    text-align: center;
}

.main {
    width: 75%; /* 固定宽度，不使用flex */
    height: 100%;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1); /* 添加平滑过渡效果 */
    will-change: transform; /* 提示浏览器这些属性将会变化 */
    position: relative; /* 使用相对定位 */
    transform: translateX(0); /* 初始位置 */
    .main-top {
        padding: 10px;
        height: 60px;
        width: 50%;
        float: right;
        // background-color: transparent;
        .large-red-icon {
            font-size: 30px;
            color: #389BDA;
        }
    }
    .main-room {
        height: 100%;
    }
}

.datae {
    font-size: 18px;
    position: absolute;
    top: 7px;
    left: 200px;
    color: #fff;
}

.timer {
    font-size: 18px;
    position: absolute;
    top: 7px;
    right: 200px;
    color: #fff;
}
</style>
