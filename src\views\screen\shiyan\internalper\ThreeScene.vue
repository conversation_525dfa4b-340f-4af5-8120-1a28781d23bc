<template>
    <div class="app-container">
      <button @click="isMotorsVis">测试按钮</button>
      <div ref="threeContainer" id = "threeContainer" ></div>
    </div>
  </template>
  
  <script>
  import { defineCameraPosition , defineCard, initRenderen,updateShow3DPattern} from './Rendering/render.js'; // 确保路径正确
  import * as THREE from 'three';
  import { generateUUID } from 'three/src/math/MathUtils.js';
  import { listRmCabinet, getRmCabinet, delRmCabinet, addRmCabinet, updateRmCabinet } from "@/api/cabinet/RmCabinet";
  import { listRmRoom, getRmRoom, delRmRoom, addRmRoom, updateRmRoom } from "@/api/room/RmRoom";
  import { initModel } from './model/allModel.js';
  import { modelVis , addInnerCube} from './Visual/modelVis.js'
  import Editor from './Editor.vue';
  export default {
    name: 'index',
    components: {
      Editor,
    },
    props: {
      show3DPattern: {
      type: Boolean,
    },
  },
    data() {
      return {
        innerContainer: true, // 初始状态为可见
        selectedCabinet: null, // 用于存储选中的机柜信息
        index : null,
        model : null, // 存储所有模型
        motor : null,
        // result: null, //存储所有模型
        // motor2: null, // 存储机柜外框
        // result2: null,
        card: null,
        obj: null,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 20,
          deptId: null,
          status: null,
          remarks: null,
          cabinetName: null,
          cabinetCode: null,
          roomId: null,
          roomName: null,
          totalU: null,
          manufacturer: null,
          cabinetType: null,
          length: null,
          width: null,
          height: null,
          positionX: null,
          positionY: null,
          orientation: null,
          modelId: null,
          modelName: null
        },
          // 机柜模板管理表格数据
          RmCabinetModelList: [],
          RmRoomList: [],
      };
    },
    mounted() {


    },
    
    async created() {
      // console.log('left ========',document.getElementById('left'));
      
    //   await this.getList();    
        await this.getList();
        await defineCameraPosition(this.RmRoomList);
        let model = await initModel(this.RmCabinetModelList,this.RmRoomList);
        this.$emit('update-RmCabinetModelList',this.RmCabinetModelList);
        this.$emit('update-RmRoomList',this.RmRoomList);
        if (!model || !model.getObjectByName("motor")) {
        console.error('Model initialization failed');
        return;
    }
        this.model = model;
        this.motor = model.getObjectByName("motor");
        this.obj = initRenderen(this.model, this.updateSelectedCabinet);
        console.log("RmCabinetModelList",this.RmCabinetModelList);
        console.log("RmRoomList",this.RmRoomList);
        // console.log("show3DPattern",this.show3DPattern);

    },
    watch: {
      innerContainer(newVal){
        modelVis(this.motor,newVal);
        addInnerCube(this.motor,newVal,this.RmCabinetModelList);
  },
      show3DPattern(newVal) {
        updateShow3DPattern(newVal);        
      },
  selectedCabinet(newVal) {
    // console.log("newVal==",newVal);
    this.$emit('update-selected-cabinet', newVal); // 触发自定义事件

  },
    },
    methods: {
        async getList() {
            this.loading = true;
            try {
                const RmRoomList = await listRmRoom(this.queryParams);
                this.RmRoomList = RmRoomList.rows;
                this.total = RmRoomList.total;
                const RmCabinetModelList = await listRmCabinet(this.queryParams);
                this.RmCabinetModelList = RmCabinetModelList.rows;
                this.total = RmCabinetModelList.total;
            } catch (error) {
                console.error('Failed to fetch data:', error);
            } finally {
                this.loading = false;
            }
        },
    //   handleCardClick(event) {
    //     event.stopPropagation();
    //   },
    //   async getList() {
    //     this.loading = true;
    //     listRmRoom(this.queryParams).then(response => {
    //       this.RmRoomList = response.rows;
    //       this.total = response.total;
    //       this.loading = false;
    //     });
    //     listRmCabinet(this.queryParams).then(response => {
    //       this.RmCabinetModelList = response.rows;
    //       // console.log("RmCabinetModelList",this.RmCabinetModelList);
    //       // console.log("RmRoomList",this.RmRoomList);
    //       this.total = response.total;
    //       this.loading = false;
    //       this.initCard();
          
    //     });
    //   },
    //   initCard() {
    //     const compDiv = this.$refs.cardId;
    //     this.card = compDiv;
    //     const threeContainer = this.$refs.threeContainer;
    //     const miniContainer = this.$refs.miniContainer;
    //     const result = index(compDiv, this.updateSelectedCabinet,
    //     threeContainer,this.RmCabinetModelList,
    //     this.RmRoomList,miniContainer
    //   );
    //     this.result = result;
    //     this.motors = result.getObjectByName('motor').getObjectByName('jigui');
    //     // this.miniContainer = miniContainer;
    //     console.log('this.motor2',this.motor2);
        
    //   },
      updateSelectedCabinet(cabinetData,index) {
        if(cabinetData && index){
          this.selectedCabinet = cabinetData;
          this.index = index;
          // console.log('this.selectedCabinet====',this.selectedCabinet);
          // console.log('this.index====',this.index);
        }
        
        },
      isMotorsVis() {
        this.innerContainer = !this.innerContainer;
        
      },
        
    }
  };
  </script>
  
  <style lang="scss" scoped>
  ::v-deep.app-container{
    padding:0px
  }
  /* 样式可以根据需要调整 */
  // .card {
  //   /* border: red 5px solid; */
  //   position: fixed;
  //   top: 85px;
  //   right: 0px;
  //   z-index: 1;
  //   height: 700px;
  //   width: 30%;
  //   position: absolute;
  //   display: flex;
  //   flex:row;
  //   color: rgb(253, 31, 31);
  //   background: rgb(113, 243, 178);
  //   opacity: 0; /* 初始透明度为0 */
  //   transition: opacity 1.5s ease-in-out; /* 定义过渡效果 */
  //   border:1px solid blue;
  // }
  // .miniContainer {
  //   /* border: blue 5px solid; */
  //   width: 350px;
  //   height: 400px;
  //   flex: 1;
  //   /* border:1px solid red; */
  // }
  // .card-content{
  //     /* border: red 5px solid; */
  //   flex:1;
  // }
  /* .abc {
    padding: 0px;
    margin: 0px;
    width: 100%;
  } */
  /* .lvse {
    height: 100vh;
    width: 100%;
    border: 1px solid red;
    transition: width 0.3s ease; /* 添加过渡效果 */
  /* } */
  </style>
  