<template>
    <div class="app-container">
      <button @click="isMotorsVis">测试按钮</button>
      <div ref="threeContainer" id = "threeContainer" ></div>
      <Editor  :selectedCabinet = "this.selectedCabinet" :index = "this.index" />
      <!-- <div class="card" ref="cardId" type="module" @click="handleCardClick"> -->
        <!-- <Editor :selectedCabinet="this.selectedCabinet"  /> -->
          <!-- <div class="miniContainer" ref="miniContainer"></div>
          <div class="card-content">
            <p v-if="selectedCabinet">机柜编码: {{ selectedCabinet.cabinetCode }}</p>
            <p v-if="selectedCabinet">机柜名称: {{ selectedCabinet.cabinetName }}</p>
            <p v-if="selectedCabinet">机柜类型: {{ selectedCabinet.cabinetType }}</p>
            <p v-if="selectedCabinet">创建人: {{ selectedCabinet.createBy }}</p>
            <p v-if="selectedCabinet">地区: {{ selectedCabinet.cabinetName }}</p>
            <p v-if="selectedCabinet">归属房间: {{ selectedCabinet.roomName }}</p>
            <p v-if="selectedCabinet">归属时间: {{ selectedCabinet.createTime }}</p>
            <p v-if="selectedCabinet">机柜长: {{ selectedCabinet.length }}</p>
            <p v-if="selectedCabinet">机柜宽: {{ selectedCabinet.width }}</p>
            <p v-if="selectedCabinet">机柜高: {{ selectedCabinet.height }}</p>
          </div>
        </div> -->
    </div>
  </template>
  
  <script>
  import { defineCameraPosition , defineCard, initRenderen} from './Rendering/render.js'; // 确保路径正确
  import * as THREE from 'three';
  import { listRmCabinet, getRmCabinet, delRmCabinet, addRmCabinet, updateRmCabinet } from "@/api/cabinet/RmCabinet";
  import { listRmRoom, getRmRoom, delRmRoom, addRmRoom, updateRmRoom } from "@/api/room/RmRoom";
  import { initModel } from './model/allModel.js';
  import { modelVis , addInnerCube} from './Visual/modelVis.js'
  import Editor from './Editor.vue';
  export default {
    name: 'index',
    components: {
      Editor,
    },
    data() {
      return {
        innerContainer: true, // 初始状态为可见
        selectedCabinet: null, // 用于存储选中的机柜信息
        index : null,
        model : null, // 存储所有模型
        motor : null,
        // result: null, //存储所有模型
        // motor2: null, // 存储机柜外框
        // result2: null,
        card: null,
        obj: null,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 20,
          deptId: null,
          status: null,
          remarks: null,
          cabinetName: null,
          cabinetCode: null,
          roomId: null,
          roomName: null,
          totalU: null,
          manufacturer: null,
          cabinetType: null,
          length: null,
          width: null,
          height: null,
          positionX: null,
          positionY: null,
          orientation: null,
          modelId: null,
          modelName: null
        },
          // 机柜模板管理表格数据
          RmCabinetModelList: [],
          RmRoomList: [],
      };
    },
    mounted() {


    },
    async created() {
    //   await this.getList();
        await this.getList();
        await defineCameraPosition(this.RmRoomList);
        let model = await initModel(this.RmCabinetModelList,this.RmRoomList);
        if (!model || !model.getObjectByName("motor")) {
        console.error('Model initialization failed');
        return;
    }
        this.model = model;
        this.motor = model.getObjectByName("motor");
        this.obj = initRenderen(this.model,this.updateSelectedCabinet);
        console.log("RmCabinetModelList",this.RmCabinetModelList);
        console.log("RmRoomList",this.RmRoomList);
        
    },
    watch: {
      innerContainer(newVal){
        modelVis(this.motor,newVal);
        addInnerCube(this.motor,newVal,this.RmCabinetModelList);
    //     this.motorVis(newVal);
    //     if (!newVal) {
    //     let i = 0;
    //     this.motor.traverse(function(child) {
    //       if (child.isMesh) {
    //       const cabinet = this.RmCabinetModelList[i++];
    //       const length = cabinet.length / 1300; // 假设单位是毫米，转换为米
    //       const width = cabinet.width / 1000; // 假设单位是毫米，转换为米
    //       const height = cabinet.height / 1500 + Math.random() * 1.3 - 0.9;
    //       const innerCubeGeometry = new THREE.BoxGeometry(length, height, width); // 较小的正方体
    //       let innerCubeMaterial;
    //       if(height<cabinet.height/2000)
    //       innerCubeMaterial = new THREE.MeshBasicMaterial({ color: 0x37ff00, transparent: true, opacity: 0.7 });
    //       else
    //       innerCubeMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000, transparent: true, opacity: 0.7 });
    //       const innerCubeMesh = new THREE.Mesh(innerCubeGeometry, innerCubeMaterial);
    //       innerCubeMesh.position.copy(child.position);
    //       const worldPosition = new THREE.Vector3();
    //       child.getWorldPosition(worldPosition);
    //       innerCubeMesh.position.y = worldPosition.y + height / 2 - child.geometry.parameters.height/2;
    //       innerCubeMesh.rotation.copy(child.rotation)

    //       this.model.add(innerCubeMesh);
    //       if (!child.userData.innerCube) {
    //         child.userData.innerCube = innerCubeMesh;
    //       }
    //     }
    //   }.bind(this));
    // } else {
    //   // 移除正方体
    //   this.motor.traverse(function(child) {
    //     if (child.isMesh && child.userData.innerCube) {
    //       this.model.remove(child.userData.innerCube);
    //       delete child.userData.innerCube;
    //     }
    //   }.bind(this));
    // }
  }      
    },
    methods: {
        async getList() {
            this.loading = true;
            try {
                const RmRoomList = await listRmRoom(this.queryParams);
                this.RmRoomList = RmRoomList.rows;
                this.total = RmRoomList.total;
                const RmCabinetModelList = await listRmCabinet(this.queryParams);
                this.RmCabinetModelList = RmCabinetModelList.rows;
                this.total = RmCabinetModelList.total;
            } catch (error) {
                console.error('Failed to fetch data:', error);
            } finally {
                this.loading = false;
            }
        },
    //   handleCardClick(event) {
    //     event.stopPropagation();
    //   },
    //   async getList() {
    //     this.loading = true;
    //     listRmRoom(this.queryParams).then(response => {
    //       this.RmRoomList = response.rows;
    //       this.total = response.total;
    //       this.loading = false;
    //     });
    //     listRmCabinet(this.queryParams).then(response => {
    //       this.RmCabinetModelList = response.rows;
    //       // console.log("RmCabinetModelList",this.RmCabinetModelList);
    //       // console.log("RmRoomList",this.RmRoomList);
    //       this.total = response.total;
    //       this.loading = false;
    //       this.initCard();
          
    //     });
    //   },
    //   initCard() {
    //     const compDiv = this.$refs.cardId;
    //     this.card = compDiv;
    //     const threeContainer = this.$refs.threeContainer;
    //     const miniContainer = this.$refs.miniContainer;
    //     const result = index(compDiv, this.updateSelectedCabinet,
    //     threeContainer,this.RmCabinetModelList,
    //     this.RmRoomList,miniContainer
    //   );
    //     this.result = result;
    //     this.motors = result.getObjectByName('motor').getObjectByName('jigui');
    //     // this.miniContainer = miniContainer;
    //     console.log('this.motor2',this.motor2);
        
    //   },
      updateSelectedCabinet(cabinetData,index) {
        if(cabinetData && index){
          this.selectedCabinet = cabinetData;
          this.index = index;
          console.log('this.selectedCabinet====',this.selectedCabinet);
          console.log('this.index====',this.index);
        }
        
        },
      isMotorsVis() {
        this.innerContainer = !this.innerContainer;
        
      },
      motorVis(newVal) {
        const model = this.model;
        if (newVal) {
          this.motor.traverse(function(child) {
          if (child.isMesh) {
            child.material.forEach(material => {
              material.transparent = true;
              material.depthWrite = false;
              material.opacity = 1.0;
              model.remove(child.userData.edges);
              delete child.userData.edges;

          });
          }
        });
        }
        else {
          this.motor.traverse(function(child) {
          if (child.isMesh) {
            child.material.forEach(material => {
              material.transparent = true;
              material.depthWrite = false;
              material.opacity = 0.3;
          });
              const edgesGeometry = new THREE.EdgesGeometry(child.geometry);
              const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x003efa, linewidth: 1.3 });
              const edges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
              const worldPosition = new THREE.Vector3();
              child.getWorldPosition(worldPosition);
              edges.position.copy(worldPosition);
              edges.rotation.copy(child.rotation);
              edges.name = 'edges';
              model.add(edges);
  
              child.userData.edges = edges;
              child.userData.edges.visible = !newVal;
          }
  
        });
        }
      },
  
    }
  };
  </script>
  
  <style lang="scss" scoped>
  ::v-deep.app-container{
    padding:0px
  }
  /* 样式可以根据需要调整 */
  // .card {
  //   /* border: red 5px solid; */
  //   position: fixed;
  //   top: 85px;
  //   right: 0px;
  //   z-index: 1;
  //   height: 700px;
  //   width: 30%;
  //   position: absolute;
  //   display: flex;
  //   flex:row;
  //   color: rgb(253, 31, 31);
  //   background: rgb(113, 243, 178);
  //   opacity: 0; /* 初始透明度为0 */
  //   transition: opacity 1.5s ease-in-out; /* 定义过渡效果 */
  //   border:1px solid blue;
  // }
  // .miniContainer {
  //   /* border: blue 5px solid; */
  //   width: 350px;
  //   height: 400px;
  //   flex: 1;
  //   /* border:1px solid red; */
  // }
  // .card-content{
  //     /* border: red 5px solid; */
  //   flex:1;
  // }
  /* .abc {
    padding: 0px;
    margin: 0px;
    width: 100%;
  } */
  /* .lvse {
    height: 100vh;
    width: 100%;
    border: 1px solid red;
    transition: width 0.3s ease; /* 添加过渡效果 */
  /* } */
  </style>
  