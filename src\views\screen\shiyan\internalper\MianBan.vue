<template>
  <div class="mianban-container" v-if="visible" @click.stop>
    <div class="mianban-content">
      <div class="mianban-header">
        <h3>面板</h3>
        <div class="close-btn" @click="closePanel">×</div>
      </div>
      <div class="mianban-body">
        <div class="mianban-layout">
          <!-- 基本信息部分 -->
          <div class="info-section">
            <h4>{{ selectedBoard ? '板卡信息' : '设备信息' }}</h4>
            <!-- 面板信息 - 当没有选中板卡时显示 -->
            <el-form ref="form" :model="currentSwitchData" label-width="120px" v-if="!selectedBoard && currentSwitchData" class="info-form">
                <el-form-item
                  v-for="field in switchInfoFields"
                  :key="field.prop"
                  class="label"
                  :label="field.label"
                  :prop="field.prop"
                >
                  <span class="value" :class="field.valueClass ? field.valueClass(currentSwitchData) : ''">
                    {{ field.formatter ? field.formatter(currentSwitchData) : (currentSwitchData[field.prop] || '未知') }}
                  </span>
                </el-form-item>
            </el-form>

            <!-- 板卡信息 - 当选中板卡时显示 -->
            <div v-if="boardLoading" class="loading-container">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载板卡数据...</div>
            </div>
            <el-form ref="boardForm" :model="selectedBoard" label-width="120px" v-if="selectedBoard && !boardLoading" class="info-form">
                <el-form-item class="label" label="所属模板：">
                  <span class="value">{{ selectedBoard.belongTemplate || 'GTGO' }}</span>
                </el-form-item>
                <el-form-item class="label" label="所属板卡：">
                  <span class="value">{{ selectedBoard.slotNumber || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="板卡名称：">
                  <span class="value">{{ selectedBoard.boardName || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="板卡号：">
                  <span class="value">{{ selectedBoard.boardNumber || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="板卡型号：">
                  <span class="value">{{ selectedBoard.model || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="板卡品牌：">
                  <span class="value">{{ selectedBoard.brand || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="长度(mm)：">
                  <span class="value">{{ selectedBoard.length || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="宽度(mm)：">
                  <span class="value">{{ selectedBoard.width || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="高度(mm)：">
                  <span class="value">{{ selectedBoard.height || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="所在设备位置X：">
                  <span class="value">{{ selectedBoard.positionX || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="所在设备位置Y：">
                  <span class="value">{{ selectedBoard.positionY || '未知' }}</span>
                </el-form-item>
                <el-form-item class="label" label="板卡状态：">
                  <span class="value" :class="{'status-normal': selectedBoard.status === '0'}">
                    {{ selectedBoard.status === '1' ? '停用' : '正常' }}
                  </span>
                </el-form-item>
                <el-form-item class="label" label="备注说明：" v-if="selectedBoard.remarks">
                  <span class="value">{{ selectedBoard.remarks || '' }}</span>
                </el-form-item>
            </el-form>
          </div>

          <!-- 板卡信息部分 - 16列固定板卡 -->
          <div class="slot-section" v-if="currentSwitchData">
            <h4>面板个数 - {{ currentSwitchData.rmEquipmentSlotList ? currentSwitchData.rmEquipmentSlotList.length : 0 }}</h4>
            <div class="slots-container">
              <!-- 单行板卡布局 -->
              <div class="rack-container">
                <!-- 遍历1-16列 -->
                <div
                  v-for="colIndex in 16"
                  :key="'col-'+colIndex"
                  class="rack-column"
                >
                  <!-- 列标签 -->
                  <div class="column-label">{{ colIndex }}</div>

                  <!-- 板卡容器 -->
                  <div class="slot-positions">
                    <!-- 如果该列没有板卡，显示一个空板卡 -->
                    <div
                      v-if="!getSlotsInColumn(colIndex).length"
                      class="rack-slot slot-empty"
                      @click="selectedBoard = null"
                      style="height: 100%;"
                    >
                      <span class="empty-slot-label">空</span>
                    </div>

                    <!-- 如果该列有板卡，统一处理单个或多个板卡 -->
                    <div
                      v-for="(slot, index) in getSlotsInColumn(colIndex)"
                      :key="slot.uuid"
                      class="rack-slot"
                      :class="{'slot-active': slot.status === '1'}"
                      :style="getSlotStyle(colIndex)"
                      @mouseover="(event) => showSlotInfo(slot, event)"
                      @mouseleave="hideSlotInfo"
                      @click="() => handleSlotSingleClick(colIndex, index)"
                      @dblclick="() => handleSlotClick(colIndex, index)"
                    >
                      <div class="slot-content">
                        <!-- 左侧端口 -->
                        <div class="ports-left">
                          <div
                            v-for="(port, portIndex) in getLeftPorts(slot.uuid)"
                            :key="'left-' + portIndex"
                            class="port-item"
                            :class="getPortStatusClass(port)"
                            :style="{
                              height: getPortHeightPercentage(slot.uuid) + '%'
                            }"
                            @mouseenter="(event) => showPortInfo(port, event)"
                            @mouseleave="hidePortInfo"
                          >
                          </div>
                        </div>

                        <!-- 右侧端口 -->
                        <div class="ports-right">
                          <div
                            v-for="(port, portIndex) in getRightPorts(slot.uuid)"
                            :key="'right-' + portIndex"
                            class="port-item"
                            :class="getPortStatusClass(port)"
                            :style="{
                              height: getPortHeightPercentage(slot.uuid) + '%'
                            }"
                            @mouseenter="(event) => showPortInfo(port, event)"
                            @mouseleave="hidePortInfo"
                          >
                          </div>
                        </div>

                        <!-- 板卡信息 -->
                        <span class="slot-number-label">{{ slot.slotNumber }}</span>
                        <span class="slot-status-label" :class="{'status-active': slot.status === '1'}">
                          {{ slot.status === '1' ? '已使用' : '未使用' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无数据提示 -->
              <div v-if="!currentSwitchData.rmEquipmentSlotList || !currentSwitchData.rmEquipmentSlotList.length" class="no-slots">
                暂无板卡信息
              </div>
            </div>
          </div>
        </div>

        <!-- 板卡信息悬浮框 -->
        <div class="slot-tooltip" v-if="activeSlot" :style="tooltipStyle">
          <div class="tooltip-header">
            <span class="tooltip-title">{{ activeSlot.slotNumber }}号板卡</span>
            <span class="tooltip-status" :class="{'status-active': activeSlot.status === '1'}">
              {{ activeSlot.status === '1' ? '已使用' : '未使用' }}
            </span>
          </div>
          <div class="tooltip-content">
            <div class="tooltip-item">
              <span class="tooltip-label">位置:</span>
              <span class="tooltip-value">X: {{ activeSlot.positionX || 0 }}, Y: {{ activeSlot.positionY || 0 }}</span>
            </div>
            <div class="tooltip-item">
              <span class="tooltip-label">尺寸:</span>
              <span class="tooltip-value">{{ activeSlot.width || 100 }} × {{ activeSlot.height || 60 }}</span>
            </div>
            <div class="tooltip-item" v-if="activeSlot.boardId">
              <span class="tooltip-label">板卡ID:</span>
              <span class="tooltip-value">{{ activeSlot.boardId }}</span>
            </div>
            <div class="tooltip-item" v-if="activeSlot.remarks">
              <span class="tooltip-label">备注:</span>
              <span class="tooltip-value">{{ activeSlot.remarks }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 端口提示框 -->
    <Port
      :visible="showPortTooltip"
      :portData="activePort"
      :position="portTooltipPosition"
    />
  </div>
</template>

<script>
import { EventBus } from '@/utils/eventBus';
import { getRmEquipment } from "@/api/equipment/RmEquipment";
import { listRmEquipmentBoard, getRmEquipmentBoard } from "@/api/equipment/RmEquipmentBoard";
import Port from './Port.vue';

export default {
  name: 'MianBan',
  components: {
    Port
  },
  data() {
    return {
      visible: false,
      switchData: null,
      activeSlot: null, // 当前激活的板卡
      tooltipPosition: { x: 0, y: 0 }, // 提示框位置
      mousePosition: { x: 0, y: 0 }, // 鼠标位置
      selectedBoard: null, // 当前选中的板卡
      boardLoading: false, // 板卡数据加载状态
      boardData: null, // 板卡详细数据
      allBoardPortsData: {}, // 存储所有板卡的端口数据
      // 端口提示框相关
      showPortTooltip: false, // 是否显示端口提示框
      activePort: null, // 当前激活的端口
      portTooltipPosition: { x: 0, y: 0 } // 端口提示框位置
    };
  },
  computed: {
    tooltipStyle() {
      // 根据鼠标位置计算提示框样式
      return {
        left: (this.mousePosition.x + 10) + 'px',
        top: (this.mousePosition.y - 10) + 'px'
      };
    },

    // 当前交换机数据
    currentSwitchData() {
      return this.switchData && this.switchData.userData && this.switchData.userData.switchData || null;
    },

    // 交换机信息字段配置
    switchInfoFields() {
      return [
        { prop: 'equipmentName', label: '设备名称：' },
        { prop: 'equipmentCode', label: '设备编号：' },
        { prop: 'model', label: '设备型号：' },
        { prop: 'type', label: '设备类型：' },
        { prop: 'manufacturer', label: '制造商：' },
        { prop: 'ipAddress', label: 'IP地址：' },
        {
          prop: 'status',
          label: '状态：',
          formatter: (data) => data.status === '1' ? '正常' : (data.status || '未知'),
          valueClass: (data) => ({ 'status-normal': data.status === '正常' || data.status === '1' })
        },
        { prop: 'installDate', label: '安装日期：' },
        { prop: 'startU', label: '起始U位：' },
        { prop: 'occupiedU', label: '占据U位：' },
        {
          prop: 'rmEquipmentSlotList',
          label: '板卡数：',
          formatter: (data) => data.rmEquipmentSlotList ? data.rmEquipmentSlotList.length : '未知'
        }
      ];
    }
  },
  mounted() {
    this.initEventListeners();
    // 添加鼠标移动事件监听
    document.addEventListener('mousemove', this.handleMouseMove);
  },
  methods: {
    initEventListeners() {
      // 监听交换机点击事件
      EventBus.$on('switch-clicked', (data) => {
        this.switchData = data;
        this.visible = true;

        // 如果userData中有交换机数据且有uuid，则通过API获取详细数据
        if (data.userData && data.userData.switchData && data.userData.switchData.uuid) {
          this.fetchSwitchData(data.userData.switchData.uuid);
        }
      });

      // 监听交换机清除事件
      EventBus.$on('switch-cleared', () => {
        this.visible = false;
        this.switchData = null;
      });
    },

    // 获取交换机详细数据
    fetchSwitchData(uuid) {
      getRmEquipment(uuid).then(response => {
        console.log('MianBan获取到交换机详细数据:', response.data);

        // 更新switchData，将API返回的数据合并到原有数据中
        if (this.switchData && this.switchData.userData) {
          // 将API返回的数据存入userData
          this.switchData.userData.switchData = {
            ...this.switchData.userData.switchData,
            ...response.data
          };

          // 获取所有板卡的端口数据
          this.fetchAllBoardPortsData();
        }
      }).catch(error => {
        console.error('获取交换机数据失败:', error);
      });
    },

    // 获取所有板卡的端口数据
    fetchAllBoardPortsData() {
      // 检查是否有板卡数据
      if (!this.currentSwitchData || !this.currentSwitchData.rmEquipmentSlotList || !this.currentSwitchData.rmEquipmentSlotList.length) {
        console.log('没有找到板卡数据或板卡列表为空');
        return;
      }

      const allSlots = this.currentSwitchData.rmEquipmentSlotList;

      // 清空之前的数据
      this.allBoardPortsData = {};
      let completedRequests = 0;
      const totalSlots = allSlots.length;

      // 遍历所有板卡，获取每个板卡的端口数据
      allSlots.forEach(slot => {
        if (slot.uuid) {
          // 准备查询参数
          const queryParams = {
            pageNum: 1,
            pageSize: 100,
            slotId: slot.uuid
          };

          // 添加交换机id到查询参数中
          if (this.currentSwitchData.uuid) {
            queryParams.roomId = this.currentSwitchData.uuid;
          }

          // 获取板卡列表
          listRmEquipmentBoard(queryParams).then(response => {
            if (response.rows && response.rows.length > 0) {
              // 获取每个板卡的详细信息（包括端口）
              const boardPromises = response.rows.map(board => {
                if (board.uuid) {
                  return getRmEquipmentBoard(board.uuid).then(detailResponse => {
                    if (detailResponse.data && detailResponse.data.rmEquipmentPortList) {
                      return {
                        boardId: board.uuid,
                        boardName: board.boardName || '未知板卡',
                        slotNumber: slot.slotNumber || '未知槽位',
                        ports: detailResponse.data.rmEquipmentPortList
                      };
                    }
                    return null;
                  }).catch(error => {
                    console.error(`获取板卡 ${board.uuid} 详细信息失败:`, error);
                    return null;
                  });
                }
                return Promise.resolve(null);
              });

              // 等待所有板卡详细信息获取完成
              Promise.all(boardPromises).then(boardResults => {
                // 过滤掉null值并存储结果
                const validBoards = boardResults.filter(board => board !== null);
                if (validBoards.length > 0) {
                  this.$set(this.allBoardPortsData, slot.uuid, validBoards);
                }

                completedRequests++;
                // 当所有槽位的数据都获取完成时，打印最终结果
                if (completedRequests === totalSlots) {
                  console.log('所有板卡端口数据获取完成:', this.allBoardPortsData);
                }
              });
            } else {
              completedRequests++;
              // 当所有槽位的数据都获取完成时，打印最终结果
              if (completedRequests === totalSlots) {
                console.log('所有板卡端口数据获取完成:', this.allBoardPortsData);
              }
            }
          }).catch(error => {
            console.error(`获取槽位 ${slot.uuid} 的板卡列表失败:`, error);
            completedRequests++;
            // 当所有槽位的数据都获取完成时，打印最终结果
            if (completedRequests === totalSlots) {
              console.log('所有板卡端口数据获取完成:', this.allBoardPortsData);
            }
          });
        } else {
          completedRequests++;
          // 当所有槽位的数据都获取完成时，打印最终结果
          if (completedRequests === totalSlots) {
            console.log('所有板卡端口数据获取完成:', this.allBoardPortsData);
          }
        }
      });
    },

    // 关闭面板
    closePanel() {
      this.visible = false;
      this.selectedBoard = null; // 清除选中的板卡
      // 发送清除事件
      EventBus.$emit('switch-cleared');
    },

    // 显示板卡信息
    showSlotInfo(slot, event) {
      // 如果正在显示端口提示框，则不显示板卡提示框
      if (this.showPortTooltip) {
        return;
      }

      this.activeSlot = slot;
      if (event) {
        this.mousePosition = {
          x: event.clientX,
          y: event.clientY
        };
      }
    },

    // 隐藏板卡信息
    hideSlotInfo() {
      // 只有在没有显示端口提示框时才隐藏板卡提示框
      if (!this.showPortTooltip) {
        this.activeSlot = null;
      }
    },

    // 监听鼠标移动
    handleMouseMove(event) {
      if (this.activeSlot) {
        this.mousePosition = {
          x: event.clientX,
          y: event.clientY
        };
        // console.log('handleMouseMove==', this.mousePosition);
      }
    },

    // 获取指定列的所有板卡，并按Y坐标排序
    getSlotsInColumn(colIndex) {
      if (!this.currentSwitchData || !this.currentSwitchData.rmEquipmentSlotList) {
        return [];
      }

      // 获取所有板卡
      const allSlots = this.currentSwitchData.rmEquipmentSlotList;

      // 根据X值筛选板卡 - 直接使用positionX值确定列号
      const filteredSlots = allSlots.filter(slot => {
        // 优先使用positionX值（如果存在）
        if (slot.positionX !== undefined) {
          // X值就是列号
          return parseInt(slot.positionX) === colIndex;
        }

        // 如果没有positionX，尝试从slotNumber中提取X值
        const slotNumberParts = String(slot.slotNumber || '').split('-');
        if (slotNumberParts && slotNumberParts.length > 0) {
          const slotCol = parseInt(slotNumberParts[0]);
          return !isNaN(slotCol) && slotCol === colIndex;
        }

        return false; // 如果无法确定X值，则不包含该板卡
      });

      // // 为每个板卡提取Y坐标
      // const slotsWithY = filteredSlots.map(slot => {
      //   // 获取Y坐标，优先使用positionY
      //   let yPos = 0;
      //   if (slot.positionY !== undefined) {
      //     yPos = parseInt(slot.positionY);
      //   } else {
      //     // 尝试从slotNumber中提取Y值
      //     const slotNumberParts = String(slot.slotNumber || '').split('-');
      //     if (slotNumberParts && slotNumberParts.length > 1) {
      //       const parsedY = parseInt(slotNumberParts[1]); // 假设slotNumber的第二部分表示Y坐标
      //       if (!isNaN(parsedY)) {
      //         yPos = parsedY;
      //       }
      //     }
      //   }

      //   return {
      //     ...slot,
      //     yPosition: yPos // 添加一个yPosition属性用于排序
      //   };
      // });
      // console.log('slotsWithY::', slotsWithY);

      // 按Y坐标从大到小排序（Y坐标大的在上方）
      return filteredSlots.sort((a, b) => b.positionY - a.positionY);
    },

    // 获取板卡样式
    getSlotStyle(colIndex) {
      const slots = this.getSlotsInColumn(colIndex);
      const isOnlyOne = slots.length === 1;

      return {
        width: '100%',
        height: isOnlyOne ? '100%' : `${100 / slots.length}%`
      };
    },

    // 处理板卡单击 - 显示板卡信息
    handleSlotSingleClick(colIndex, slotIndex) {
      const slots = this.getSlotsInColumn(colIndex);

      if (slots.length > 0 && slotIndex !== undefined && slots[slotIndex]) {
        // 如果有板卡，获取板卡信息
        const slot = slots[slotIndex];
        console.log(`单击了第${colIndex}列第${slotIndex+1}个板卡，板卡号: ${slot.slotNumber}`);

        // 清除之前选中的板卡
        this.selectedBoard = null;

        // 设置加载状态
        this.boardLoading = true;

        // 获取板卡对应的板卡信息
        this.fetchBoardData(slot);
      } else {
        console.log(`单击了第${colIndex}列空板卡`);
        // 清除选中的板卡
        this.selectedBoard = null;
      }
    },

    // 获取板卡详细数据
    fetchBoardData(slot) {
      // 准备查询参数
      const queryParams = {
        pageNum: 1,
        pageSize: 100, // 增加页面大小，确保获取所有板卡
        slotId: slot.uuid // 使用板卡id
      };

      // 从点击的板卡获取真实的参数数据
      // 添加交换机id（roomId）到查询参数中
      if (this.currentSwitchData && this.currentSwitchData.uuid) {
        queryParams.roomId = this.currentSwitchData.uuid;
      }

      // 使用listRmEquipmentBoard方法获取板卡数据
      listRmEquipmentBoard(queryParams).then(response => {
        // console.log('获取到板卡列表:', response.rows);

        // 更新板卡数据
        if (response.rows && response.rows.length > 0) {
          // 使用第一个板卡作为选中的板卡
          this.selectedBoard = response.rows[0];

          // 如果板卡有uuid，获取更详细的信息
          if (this.selectedBoard.uuid) {
            this.fetchBoardDetail(this.selectedBoard.uuid);
          } else {
            this.boardLoading = false;
          }
        } else {
          console.log('未找到相关板卡数据');
          this.selectedBoard = null;
          this.boardLoading = false;
        }
      }).catch(error => {
        console.error('获取板卡列表失败:', error);
        this.selectedBoard = null;
        this.boardLoading = false;
      });
    },

    // 获取单个板卡的详细信息
    fetchBoardDetail(boardId) {
      getRmEquipmentBoard(boardId).then(response => {
        if (response.data) {
          console.log('获取到板卡详细信息:', response.data);

          // 更新选中的板卡信息
          this.selectedBoard = {
            ...this.selectedBoard,
            ...response.data
          };
        }
        this.boardLoading = false;
      }).catch(error => {
        console.error('获取板卡详细信息失败:', error);
        this.boardLoading = false;
      });
    },

    // 处理板卡双击 - 打开CaoWei.vue页面
    handleSlotClick(colIndex, slotIndex) {
      const slots = this.getSlotsInColumn(colIndex);
      console.log('slots::', slots);

      if (slots.length > 0 && slotIndex !== undefined && slots[slotIndex]) {
        // 如果有板卡，触发板卡点击事件
        const slot = slots[slotIndex];
        console.log(`双击了第${colIndex}列第${slotIndex+1}个板卡，板卡号: ${slot.slotNumber}`);

        // 获取交换机信息并创建增强的板卡数据
        const enhancedSlot = this.currentSwitchData ? {
          ...slot,
          // 交换机ID作为roomId传递
          roomId: this.currentSwitchData.uuid,
          // 交换机名称
          equipmentName: this.currentSwitchData.equipmentName
        } : slot;

        // 触发板卡点击事件
        EventBus.$emit('slot-clicked', enhancedSlot);
      } else {
        console.log(`双击了第${colIndex}列空板卡`);
        // 这里可以添加空板卡点击处理逻辑
      }
    },

    // 获取指定槽位的所有板卡端口数据
    getSlotPorts(slotUuid) {
      if (!this.allBoardPortsData || !this.allBoardPortsData[slotUuid]) {
        return [];
      }

      // 返回该槽位下所有板卡的端口数据
      const slotBoards = this.allBoardPortsData[slotUuid];
      let allPorts = [];

      slotBoards.forEach(board => {
        if (board.ports && board.ports.length > 0) {
          allPorts = allPorts.concat(board.ports);
        }
      });

      return allPorts;
    },



    // 获取端口在板卡左侧的数据（偶数索引）
    getLeftPorts(slotUuid) {
      const ports = this.getSlotPorts(slotUuid);
      return ports.filter((_port, index) => index % 2 === 0);
    },

    // 获取端口在板卡右侧的数据（奇数索引）
    getRightPorts(slotUuid) {
      const ports = this.getSlotPorts(slotUuid);
      return ports.filter((_port, index) => index % 2 === 1);
    },

    // 计算端口高度百分比
    getPortHeightPercentage(slotUuid) {
      const ports = this.getSlotPorts(slotUuid);
      const portCount = ports.length;

      if (portCount === 0) return 0;
      if (portCount <= 8) return 12.5;
      if (portCount <= 16) return 6;

      // 对于更多端口，动态计算
      return Math.max(100 / portCount, 3); // 最小3%
    },

    // 获取端口状态样式类
    getPortStatusClass(port) {
      if (!port || port.status === undefined || port.status === null) {
        return 'port-unknown';
      }
      return port.status === 0 || port.status === '0' ? 'port-normal' : 'port-error';
    },

    // 显示端口信息
    showPortInfo(port, event) {
      // 隐藏板卡提示框
      this.activeSlot = null;

      // 显示端口提示框
      this.activePort = port;
      this.showPortTooltip = true;

      if (event) {
        this.portTooltipPosition = {
          x: event.clientX,
          y: event.clientY
        };
      }
    },

    // 隐藏端口信息
    hidePortInfo() {
      this.showPortTooltip = false;
      this.activePort = null;
    },


  },
  beforeDestroy() {
    // 移除事件监听器
    EventBus.$off('switch-clicked');
    EventBus.$off('switch-cleared');
    // 移除鼠标移动事件监听
    document.removeEventListener('mousemove', this.handleMouseMove);
    // 清除选中的板卡
    this.selectedBoard = null;
  }
};
</script>

<style scoped>
.mianban-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mianban-content {
  width: 90%;
  max-width: 1800px;
  max-height: 95vh;
  background-color: rgba(0, 58, 112, 0.9);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.mianban-header {
  padding: 15px 20px;
  background-color: rgba(0, 48, 92, 0.9);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(57, 155, 218, 0.3);
}

.mianban-header h3 {
  margin: 0;
  color: #389BDA;
  font-size: 18px;
}

.close-btn {
  color: #389BDA;
  font-size: 24px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.3s;
}

.close-btn:hover {
  background-color: rgba(57, 155, 218, 0.2);
}

.mianban-body {
  padding: 20px;
  overflow: hidden;
  flex: 1;
}

.mianban-layout {
  display: flex;
  flex-direction: row;
  gap: 20px;
  height: 100%;
}

.info-section, .slot-section {
  margin-bottom: 20px;
  background-color: rgba(0, 30, 60, 0.5);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid rgba(57, 155, 218, 0.2);
}

.info-section {
  flex: 0 0 350px; /* 固定宽度 */
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 150px);
  height: 600px; /* 固定高度，与板卡展示区域一致 */
  min-height: 600px; /* 确保最小高度 */
}

.slot-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.slots-container {
  position: relative;
  width: 100%;
  height: auto;
  min-height: 400px;
  max-height: calc(100vh - 200px);
  overflow: auto;
  border: 1px solid rgba(57, 155, 218, 0.4);
  border-radius: 4px;
  padding: 15px;
  background-color: rgba(0, 20, 40, 0.5);
}

h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #389BDA;
  font-size: 16px;
  border-bottom: 1px solid rgba(57, 155, 218, 0.2);
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.label {
  color: rgb(255, 255, 255);
  font-size: 12px;
  margin-bottom: 5px;
}

.value {
  color: #ffffff;
  font-size: 14px;
}

.status-normal {
  color: #52c41a;
}

/* 机柜容器样式 */
.rack-container {
  width: 100%;
  background-color: #d9d9d9;
  border-radius: 4px;
  border: 1px solid #bbb;
  padding: 10px;
  display: flex;
  flex-direction: row;
  gap: 1px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 600px; /* 增加高度，使板卡显示更清晰 */
}

/* 列样式 */
.rack-column {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid #aaa;
  background-color: #f0f0f0;
}

/* 列标签样式 */
.column-label {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 12px;
  color: #666;
  font-weight: bold;
  z-index: 2;
}

/* 板卡样式 */
.rack-slot {
  width: 100%;
  border: 1px solid #aaa;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1px; /* 添加底部间距，使板卡之间有明显分隔 */
}

.rack-slot:hover {
  background-color: #e0e0e0;
  box-shadow: 0 0 10px rgba(57, 155, 218, 0.5);
  z-index: 5;
}

/* 板卡位置容器 */
.slot-positions {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
}

/* 板卡内容样式 */
.slot-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 5px;
  position: relative;
}

/* 端口容器样式 - 绝对定位覆盖在板卡上 */
.ports-left {
  position: absolute;
  left: 2px;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  width: 12px;
  z-index: 10;
}

.ports-right {
  position: absolute;
  right: 2px;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  width: 12px;
  z-index: 10;
}

/* 端口样式 */
.port-item {
  width: 10px;
  border: 1px solid #2E7D32;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 6px;
}

/* 端口状态样式 */
.port-normal {
  background-color: #4CAF50;
  border-color: #2E7D32;
}

.port-error {
  background-color: #f44336;
  border-color: #c62828;
}

.port-unknown {
  background-color: #ff9800;
  border-color: #f57c00;
}

/* 端口悬停效果 */
.port-item:hover {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
  transform: scale(1.2);
  z-index: 15;
}

.port-normal:hover {
  background-color: #66BB6A;
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.7);
}

.port-error:hover {
  background-color: #ef5350;
  box-shadow: 0 0 8px rgba(244, 67, 54, 0.7);
}

.port-unknown:hover {
  background-color: #ffb74d;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.7);
}

/* 板卡编号标签样式 */
.slot-number-label {
  color: #333;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 5px;
}

/* 板卡位置标签样式 */
.slot-position-label {
  color: #666;
  font-size: 12px;
  text-align: center;
}

/* 空板卡样式 */
.slot-empty {
  background-color: #f5f5f5;
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(200, 200, 200, 0.5) 10px,
    rgba(200, 200, 200, 0.5) 20px
  );
}

.empty-slot-label {
  color: #999;
  font-size: 14px;
  font-style: italic;
}

/* 活动板卡样式 */
.slot-active {
  background-color: rgba(0, 80, 40, 0.2);
  border-color: rgba(82, 196, 26, 0.8);
}

.slot-active:hover {
  background-color: rgba(0, 100, 50, 0.3);
  border-color: rgba(82, 196, 26, 1);
  box-shadow: 0 0 15px rgba(82, 196, 26, 0.5);
}

/* 无数据提示 */
.no-slots {
  text-align: center;
  padding: 30px;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  font-size: 16px;
}

.slot-active {
  background-color: rgba(0, 80, 40, 0.7);
  border-color: rgba(82, 196, 26, 0.8);
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.5);
}

.slot-active:hover {
  background-color: rgba(0, 100, 50, 0.8);
  border-color: rgba(82, 196, 26, 1);
  box-shadow: 0 0 15px rgba(82, 196, 26, 0.8);
}

.slot-number-label {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

/* 提示框样式 */
.slot-tooltip {
  position: fixed;
  background-color: rgba(0, 30, 60, 0.95);
  border: 2px solid rgba(57, 155, 218, 0.7);
  border-radius: 8px;
  padding: 15px;
  min-width: 250px;
  max-width: 350px;
  z-index: 1100;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
  pointer-events: none;
  backdrop-filter: blur(5px);
  transform: translateY(-10px);
  max-height: 400px;
  overflow-y: auto;
  overflow-x: auto;

}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 2px solid rgba(57, 155, 218, 0.5);
  padding-bottom: 8px;
}

.tooltip-title {
  color: #ffffff;
  font-weight: bold;
  font-size: 16px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.tooltip-status {
  font-size: 14px;
  padding: 3px 8px;
  border-radius: 12px;
  background-color: rgba(255, 85, 0, 0.3);
  color: #ff7733;
  font-weight: bold;
}

.status-active {
  background-color: rgba(82, 196, 26, 0.3);
  color: #6dff3e;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.tooltip-item {
  display: flex;
  flex-direction: column;
  background-color: rgba(0, 40, 80, 0.5);
  padding: 8px;
  border-radius: 6px;
  border-left: 3px solid rgba(57, 155, 218, 0.7);
}

.tooltip-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-bottom: 4px;
  font-weight: bold;
}

.tooltip-value {
  color: #ffffff;
  font-size: 15px;
}

.no-slots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

/* 加载状态容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: calc(100% - 40px); /* 减去标题高度，与表单区域一致 */
  color: #389BDA;
}

/* 加载旋转动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(56, 155, 218, 0.3);
  border-radius: 50%;
  border-top-color: #389BDA;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 加载文本 */
.loading-text {
  font-size: 14px;
  color: #389BDA;
  font-weight: bold;
}

/* 信息表单样式 */
.info-form {
  height: calc(100% - 40px); /* 减去标题高度 */
  overflow-y: auto;
  padding-right: 10px;
}
</style>